package com.zl.webpagesave.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

@Composable
fun ChatScreen() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF5F5F5))
    ) {
        // 顶部标题栏
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            shape = RoundedCornerShape(0.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "聊天",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                Row {
                    IconButton(onClick = { }) {
                        Icon(
                            imageVector = Icons.Default.Search,
                            contentDescription = "搜索",
                            tint = Color.Gray
                        )
                    }
                    IconButton(onClick = { }) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "添加",
                            tint = Color.Gray
                        )
                    }
                }
            }
        }
        
        // 聊天列表
        LazyColumn(
            modifier = Modifier.fillMaxSize()
        ) {
            items(getChatList()) { chat ->
                ChatListItem(chat)
            }
        }
    }
}

@Composable
fun ChatListItem(chat: ChatData) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 4.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 头像
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .background(
                        color = chat.avatarColor,
                        shape = CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = chat.name.first().toString(),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = chat.name,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier.weight(1f),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    Text(
                        text = chat.time,
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                }
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = chat.lastMessage,
                        fontSize = 14.sp,
                        color = Color.Gray,
                        modifier = Modifier.weight(1f),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    
                    if (chat.unreadCount > 0) {
                        Box(
                            modifier = Modifier
                                .size(20.dp)
                                .background(
                                    color = Color(0xFFD32F2F),
                                    shape = CircleShape
                                ),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = if (chat.unreadCount > 99) "99+" else chat.unreadCount.toString(),
                                fontSize = 10.sp,
                                color = Color.White,
                                fontWeight = FontWeight.Bold
                            )
                        }
                    }
                }
            }
        }
    }
}

data class ChatData(
    val name: String,
    val lastMessage: String,
    val time: String,
    val unreadCount: Int,
    val avatarColor: Color
)

fun getChatList(): List<ChatData> {
    return listOf(
        ChatData(
            name = "张三",
            lastMessage = "你好，请问商品还有库存吗？",
            time = "10:30",
            unreadCount = 2,
            avatarColor = Color(0xFF2196F3)
        ),
        ChatData(
            name = "李四",
            lastMessage = "好的，谢谢！",
            time = "09:45",
            unreadCount = 0,
            avatarColor = Color(0xFF4CAF50)
        ),
        ChatData(
            name = "王五",
            lastMessage = "什么时候发货？",
            time = "昨天",
            unreadCount = 1,
            avatarColor = Color(0xFFFF9800)
        ),
        ChatData(
            name = "赵六",
            lastMessage = "收到了，质量很好",
            time = "昨天",
            unreadCount = 0,
            avatarColor = Color(0xFF9C27B0)
        ),
        ChatData(
            name = "客服小助手",
            lastMessage = "有什么问题可以随时联系我们",
            time = "前天",
            unreadCount = 0,
            avatarColor = Color(0xFFFF5722)
        ),
        ChatData(
            name = "陈七",
            lastMessage = "这个价格能优惠吗？",
            time = "前天",
            unreadCount = 3,
            avatarColor = Color(0xFF607D8B)
        ),
        ChatData(
            name = "刘八",
            lastMessage = "已经付款了",
            time = "3天前",
            unreadCount = 0,
            avatarColor = Color(0xFF795548)
        ),
        ChatData(
            name = "周九",
            lastMessage = "快递什么时候到？",
            time = "3天前",
            unreadCount = 1,
            avatarColor = Color(0xFF3F51B5)
        ),
        ChatData(
            name = "吴十",
            lastMessage = "商品很满意，五星好评",
            time = "4天前",
            unreadCount = 0,
            avatarColor = Color(0xFF009688)
        ),
        ChatData(
            name = "系统通知",
            lastMessage = "您有新的订单消息",
            time = "5天前",
            unreadCount = 5,
            avatarColor = Color(0xFFE91E63)
        )
    )
}
